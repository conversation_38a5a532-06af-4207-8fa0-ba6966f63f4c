import { describe, it, expect, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import BrowserToolsRecords from '../BrowserToolsRecords.vue'
import type { BrowserToolRecord } from '@/types/user'

// 模拟数据
const mockRecords: BrowserToolRecord[] = [
  {
    id: 'tool_001',
    toolName: 'getSelectedElement',
    toolType: 'debug',
    executedAt: '2024-01-20T14:30:15Z',
    duration: 120,
    status: 'success',
    description: '检测用户选中的注册卡片元素',
    elementTarget: '.register-card',
    results: {
      tagName: 'DIV',
      className: 'register-card'
    },
    logs: ['开始检测选中元素...', '检测完成']
  },
  {
    id: 'tool_002',
    toolName: 'runAccessibilityAudit',
    toolType: 'audit',
    executedAt: '2024-01-20T14:28:45Z',
    duration: 2500,
    status: 'warning',
    description: '执行页面无障碍性能检查',
    results: {
      score: 85,
      violations: []
    },
    logs: ['开始无障碍审计...', '审计完成'],
    errors: ['警告: 颜色对比度不足']
  },
  {
    id: 'tool_003',
    toolName: 'takeScreenshot',
    toolType: 'screenshot',
    executedAt: '2024-01-20T14:25:30Z',
    duration: 800,
    status: 'success',
    description: '截图记录优化前的页面状态',
    screenshot: 'data:image/png;base64,test',
    results: {
      width: 1024,
      height: 768
    },
    logs: ['准备截图...', '截图完成']
  },
  {
    id: 'tool_004',
    toolName: 'runDebuggerMode',
    toolType: 'debug',
    executedAt: '2024-01-20T14:10:05Z',
    duration: 1800,
    status: 'failed',
    description: '启用调试模式进行深度分析',
    results: {
      debugPoints: 3,
      errors: 1
    },
    logs: ['启动调试模式...', '检测到异常'],
    errors: ['TypeError: Cannot read property "length" of undefined']
  }
]

describe('BrowserToolsRecords', () => {
  let wrapper: any

  beforeEach(() => {
    wrapper = mount(BrowserToolsRecords, {
      props: {
        records: mockRecords
      }
    })
  })

  it('renders correctly with records', () => {
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.browser-tools-records').exists()).toBe(true)
  })

  it('displays the correct title and record count', () => {
    const title = wrapper.find('.records-title')
    expect(title.text()).toContain('BrowserToolsMCP 执行记录')
    
    const count = wrapper.find('.records-count')
    expect(count.text()).toBe('共 4 条记录')
  })

  it('shows only first 2 records by default', () => {
    const recordItems = wrapper.findAll('.record-item')
    expect(recordItems).toHaveLength(2)
  })

  it('displays view all button when more than 2 records', () => {
    const viewAllBtn = wrapper.find('.view-all-btn')
    expect(viewAllBtn.exists()).toBe(true)
    expect(viewAllBtn.text()).toContain('查看全部记录')
    expect(viewAllBtn.text()).toContain('(4)')
  })

  it('shows all records when view all button is clicked', async () => {
    const viewAllBtn = wrapper.find('.view-all-btn')
    await viewAllBtn.trigger('click')
    
    const recordItems = wrapper.findAll('.record-item')
    expect(recordItems).toHaveLength(4)
    
    expect(viewAllBtn.text()).toContain('收起记录')
  })

  it('displays correct tool icons for different types', () => {
    const toolIcons = wrapper.findAll('.tool-icon')
    expect(toolIcons[0].text()).toBe('🐛') // debug
    expect(toolIcons[1].text()).toBe('🔍') // audit
  })

  it('displays correct status badges', () => {
    const statusBadges = wrapper.findAll('.status-badge')
    expect(statusBadges[0].text()).toBe('成功')
    expect(statusBadges[0].classes()).toContain('status-success')
    expect(statusBadges[1].text()).toBe('警告')
    expect(statusBadges[1].classes()).toContain('status-warning')
  })

  it('formats date and duration correctly', () => {
    const recordMeta = wrapper.findAll('.record-meta')
    expect(recordMeta[0].text()).toContain('01月20日 14:30:15')
    expect(recordMeta[0].text()).toContain('耗时 120ms')
    expect(recordMeta[1].text()).toContain('耗时 3s')
  })

  it('shows element target when available', () => {
    const recordMeta = wrapper.findAll('.record-meta')
    expect(recordMeta[0].text()).toContain('目标: .register-card')
  })

  it('expands record details when toggle button is clicked', async () => {
    const toggleBtn = wrapper.find('.toggle-details-btn')
    expect(toggleBtn.exists()).toBe(true)
    
    await toggleBtn.trigger('click')
    
    const expandedDetails = wrapper.find('.record-details-expanded')
    expect(expandedDetails.exists()).toBe(true)
    
    expect(toggleBtn.text()).toBe('收起详情')
  })

  it('displays screenshot button when screenshot is available', async () => {
    // 展开所有记录以访问截图记录
    const viewAllBtn = wrapper.find('.view-all-btn')
    await viewAllBtn.trigger('click')
    
    const screenshotRecord = wrapper.findAll('.record-item')[2]
    const screenshotBtn = screenshotRecord.find('.view-screenshot-btn')
    expect(screenshotBtn.exists()).toBe(true)
    expect(screenshotBtn.text()).toBe('查看截图')
  })

  it('handles empty records gracefully', () => {
    const emptyWrapper = mount(BrowserToolsRecords, {
      props: {
        records: []
      }
    })
    
    const emptyState = emptyWrapper.find('.empty-records')
    expect(emptyState.exists()).toBe(true)
    expect(emptyState.text()).toContain('暂无执行记录')
  })

  it('does not show view all button when 2 or fewer records', () => {
    const fewRecordsWrapper = mount(BrowserToolsRecords, {
      props: {
        records: mockRecords.slice(0, 2)
      }
    })
    
    const viewAllBtn = fewRecordsWrapper.find('.view-all-btn')
    expect(viewAllBtn.exists()).toBe(false)
  })

  it('collapses expanded records when view all is toggled off', async () => {
    // 展开所有记录
    const viewAllBtn = wrapper.find('.view-all-btn')
    await viewAllBtn.trigger('click')
    
    // 展开一个记录的详情
    const toggleBtn = wrapper.find('.toggle-details-btn')
    await toggleBtn.trigger('click')
    
    let expandedDetails = wrapper.find('.record-details-expanded')
    expect(expandedDetails.exists()).toBe(true)
    
    // 收起所有记录
    await viewAllBtn.trigger('click')
    
    // 检查详情是否被收起
    expandedDetails = wrapper.find('.record-details-expanded')
    expect(expandedDetails.exists()).toBe(false)
  })

  it('displays logs and errors in expanded details', async () => {
    // 展开所有记录
    const viewAllBtn = wrapper.find('.view-all-btn')
    await viewAllBtn.trigger('click')
    
    // 展开失败记录的详情
    const failedRecord = wrapper.findAll('.record-item')[3]
    const toggleBtn = failedRecord.find('.toggle-details-btn')
    await toggleBtn.trigger('click')
    
    const expandedDetails = failedRecord.find('.record-details-expanded')
    expect(expandedDetails.exists()).toBe(true)
    
    const logsSection = expandedDetails.find('.logs-section')
    expect(logsSection.exists()).toBe(true)
    expect(logsSection.text()).toContain('启动调试模式...')
    
    const errorsSection = expandedDetails.find('.errors-section')
    expect(errorsSection.exists()).toBe(true)
    expect(errorsSection.text()).toContain('TypeError: Cannot read property')
  })
})
